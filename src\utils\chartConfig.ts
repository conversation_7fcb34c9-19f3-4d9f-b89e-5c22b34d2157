import { EChartsOption } from 'echarts';
import { formatMoneyAuto, formatTime, getFlowColor } from './formatters';

/**
 * 资金流向颜色配置 - 使用更加区分度高的颜色
 */
export const FLOW_COLORS = {
  mainNetInflow: '#e74c3c',      // 主力 - 鲜红色
  superLargeNetInflow: '#3498db', // 超大单 - 蓝色
  largeNetInflow: '#2ecc71',     // 大单 - 绿色
  mediumNetInflow: '#f39c12',    // 中单 - 橙色
  smallNetInflow: '#9b59b6',     // 小单 - 紫色
} as const;

/**
 * 图表主题配置
 */
export const CHART_THEME = {
  backgroundColor: '#ffffff',
  textColor: '#333333',
  axisLineColor: '#e5e7eb',
  splitLineColor: '#f3f4f6',
  tooltipBackgroundColor: 'rgba(255, 255, 255, 0.95)',
  tooltipBorderColor: '#e5e7eb',
} as const;

/**
 * 暗色主题配置
 */
export const DARK_CHART_THEME = {
  backgroundColor: '#1f2937',
  textColor: '#f9fafb',
  axisLineColor: '#374151',
  splitLineColor: '#4b5563',
  tooltipBackgroundColor: 'rgba(31, 41, 55, 0.95)',
  tooltipBorderColor: '#6b7280',
} as const;

/**
 * 获取基础图表配置
 * @param isDark 是否为暗色主题
 * @returns 基础配置对象
 */
export function getBaseChartConfig(isDark: boolean = false): Partial<EChartsOption> {
  const theme = isDark ? DARK_CHART_THEME : CHART_THEME;
  
  return {
    backgroundColor: theme.backgroundColor,
    textStyle: {
      color: theme.textColor,
      fontFamily: 'system-ui, -apple-system, sans-serif',
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    tooltip: {
      backgroundColor: theme.tooltipBackgroundColor,
      borderColor: theme.tooltipBorderColor,
      borderWidth: 1,
      textStyle: {
        color: theme.textColor,
      },
    },
    legend: {
      textStyle: {
        color: theme.textColor,
      },
    },
  };
}

/**
 * 创建资金流向折线图配置
 * @param data K线数据数组
 * @param isDark 是否为暗色主题
 * @returns ECharts配置对象
 */
export function createFlowLineChartConfig(
  data: Array<{
    time: string;
    mainNetInflow: number;
    superLargeNetInflow: number;
    largeNetInflow: number;
    mediumNetInflow: number;
    smallNetInflow: number;
  }>,
  isDark: boolean = false
): EChartsOption {
  const baseConfig = getBaseChartConfig(isDark);
  const theme = isDark ? DARK_CHART_THEME : CHART_THEME;
  
  // 提取时间轴数据
  const timeData = data.map(item => formatTime(item.time));
  
  // 创建系列数据 - 使用不同的线条样式增强区分度
  const series = [
    {
      name: '主力净流入',
      type: 'line' as const,
      data: data.map(item => item.mainNetInflow),
      color: FLOW_COLORS.mainNetInflow,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 3,
        type: 'solid'
      },
      emphasis: {
        lineStyle: { width: 4 }
      }
    },
    {
      name: '超大单净流入',
      type: 'line' as const,
      data: data.map(item => item.superLargeNetInflow),
      color: FLOW_COLORS.superLargeNetInflow,
      smooth: true,
      symbol: 'rect',
      symbolSize: 5,
      lineStyle: {
        width: 2.5,
        type: 'solid'
      },
      emphasis: {
        lineStyle: { width: 3.5 }
      }
    },
    {
      name: '大单净流入',
      type: 'line' as const,
      data: data.map(item => item.largeNetInflow),
      color: FLOW_COLORS.largeNetInflow,
      smooth: true,
      symbol: 'triangle',
      symbolSize: 5,
      lineStyle: {
        width: 2.5,
        type: 'solid'
      },
      emphasis: {
        lineStyle: { width: 3.5 }
      }
    },
    {
      name: '中单净流入',
      type: 'line' as const,
      data: data.map(item => item.mediumNetInflow),
      color: FLOW_COLORS.mediumNetInflow,
      smooth: true,
      symbol: 'diamond',
      symbolSize: 5,
      lineStyle: {
        width: 2,
        type: 'dashed'
      },
      emphasis: {
        lineStyle: { width: 3 }
      }
    },
    {
      name: '小单净流入',
      type: 'line' as const,
      data: data.map(item => item.smallNetInflow),
      color: FLOW_COLORS.smallNetInflow,
      smooth: true,
      symbol: 'pin',
      symbolSize: 5,
      lineStyle: {
        width: 2,
        type: 'dotted'
      },
      emphasis: {
        lineStyle: { width: 3 }
      }
    },
  ];

  return {
    ...baseConfig,
    title: {
      text: '资金流向趋势图',
      textStyle: {
        color: theme.textColor,
        fontSize: 16,
        fontWeight: 'bold',
      },
      left: 'center',
    },
    tooltip: {
      ...baseConfig.tooltip,
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: theme.textColor,
        },
      },
      formatter: (params: any) => {
        if (!Array.isArray(params)) return '';
        
        const time = params[0]?.axisValue || '';
        let content = `<div style="margin-bottom: 4px; font-weight: bold;">${time}</div>`;
        
        params.forEach((param: any) => {
          const value = formatMoneyAuto(param.value);
          const color = param.color;
          content += `
            <div style="display: flex; align-items: center; margin: 2px 0;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 8px;"></span>
              <span style="flex: 1;">${param.seriesName}:</span>
              <span style="font-weight: bold; color: ${getFlowColor(param.value)};">${value}</span>
            </div>
          `;
        });
        
        return content;
      },
    },
    legend: {
      ...baseConfig.legend,
      top: 30,
      data: series.map(s => s.name),
      orient: 'horizontal',
      left: 'center',
      itemWidth: 25,
      itemHeight: 14,
      itemGap: 20,
      textStyle: {
        fontSize: 12,
        color: theme.textColor,
      },
      icon: 'line',
      lineStyle: {
        width: 3,
      },
    },
    xAxis: {
      type: 'category',
      data: timeData,
      axisLine: {
        lineStyle: { color: theme.axisLineColor },
      },
      axisLabel: {
        color: theme.textColor,
        rotate: 45,
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: { color: theme.axisLineColor },
      },
      axisLabel: {
        color: theme.textColor,
        formatter: (value: number) => formatMoneyAuto(value),
      },
      splitLine: {
        lineStyle: { color: theme.splitLineColor },
      },
    },
    series,
    dataZoom: [
      {
        type: 'inside',
        start: 70,
        end: 100,
      },
      {
        type: 'slider',
        start: 70,
        end: 100,
        height: 20,
        bottom: 10,
      },
    ],
  };
}

/**
 * 创建资金流向柱状图配置
 * @param data 最新的资金流向数据
 * @param isDark 是否为暗色主题
 * @returns ECharts配置对象
 */
export function createFlowBarChartConfig(
  data: {
    mainNetInflow: number;
    superLargeNetInflow: number;
    largeNetInflow: number;
    mediumNetInflow: number;
    smallNetInflow: number;
  },
  isDark: boolean = false
): EChartsOption {
  const baseConfig = getBaseChartConfig(isDark);
  const theme = isDark ? DARK_CHART_THEME : CHART_THEME;
  
  const categories = ['主力', '超大单', '大单', '中单', '小单'];
  const values = [
    data.mainNetInflow,
    data.superLargeNetInflow,
    data.largeNetInflow,
    data.mediumNetInflow,
    data.smallNetInflow,
  ];
  
  const colors = [
    FLOW_COLORS.mainNetInflow,
    FLOW_COLORS.superLargeNetInflow,
    FLOW_COLORS.largeNetInflow,
    FLOW_COLORS.mediumNetInflow,
    FLOW_COLORS.smallNetInflow,
  ];

  return {
    ...baseConfig,
    title: {
      text: '实时资金流向',
      textStyle: {
        color: theme.textColor,
        fontSize: 16,
        fontWeight: 'bold',
      },
      left: 'center',
    },
    tooltip: {
      ...baseConfig.tooltip,
      trigger: 'axis',
      formatter: (params: any) => {
        if (!Array.isArray(params) || params.length === 0) return '';
        
        const param = params[0];
        const value = formatMoneyAuto(param.value);
        const direction = param.value >= 0 ? '流入' : '流出';
        const color = getFlowColor(param.value);
        
        return `
          <div style="font-weight: bold; margin-bottom: 4px;">${param.name}净${direction}</div>
          <div style="color: ${color}; font-size: 14px; font-weight: bold;">${value}</div>
        `;
      },
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLine: {
        lineStyle: { color: theme.axisLineColor },
      },
      axisLabel: {
        color: theme.textColor,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: { color: theme.axisLineColor },
      },
      axisLabel: {
        color: theme.textColor,
        formatter: (value: number) => formatMoneyAuto(value),
      },
      splitLine: {
        lineStyle: { color: theme.splitLineColor },
      },
    },
    series: [
      {
        type: 'bar' as const,
        data: values.map((value, index) => ({
          value,
          itemStyle: {
            color: colors[index],
          },
        })),
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
          formatter: (params: any) => formatMoneyAuto(params.value),
          color: theme.textColor,
        },
      },
    ],
  };
}

/**
 * 响应式图表配置
 * @param containerWidth 容器宽度
 * @returns 响应式配置
 */
export function getResponsiveConfig(containerWidth: number): Partial<EChartsOption> {
  const isMobile = containerWidth < 768;
  
  return {
    grid: {
      left: isMobile ? '5%' : '3%',
      right: isMobile ? '5%' : '4%',
      bottom: isMobile ? '15%' : '3%',
      containLabel: true,
    },
    legend: {
      orient: isMobile ? 'horizontal' : 'horizontal',
      top: isMobile ? 'bottom' : 30,
      itemWidth: isMobile ? 15 : 25,
      itemHeight: isMobile ? 10 : 14,
      textStyle: {
        fontSize: isMobile ? 10 : 12,
      },
    },
    xAxis: {
      axisLabel: {
        fontSize: isMobile ? 10 : 12,
        rotate: isMobile ? 45 : 0,
      },
    },
    yAxis: {
      axisLabel: {
        fontSize: isMobile ? 10 : 12,
      },
    },
    dataZoom: isMobile ? [] : [
      {
        type: 'inside',
        start: 70,
        end: 100,
      },
      {
        type: 'slider',
        start: 70,
        end: 100,
        height: 20,
        bottom: 10,
      },
    ],
  };
}
